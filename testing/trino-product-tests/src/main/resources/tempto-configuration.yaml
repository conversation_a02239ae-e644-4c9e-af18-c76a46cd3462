hdfs:
  username: hive
  webhdfs:
    uri: http://${databases.hive.host}:50070

databases:
  hive:
    host: hadoop-master
    jdbc_driver_class: org.apache.hive.jdbc.HiveDriver
    jdbc_url: jdbc:hive2://${databases.hive.host}:10000
    jdbc_user: hive
    jdbc_password: na
    jdbc_pooling: false
    schema: default
    prepare_statement:
      - USE ${databases.hive.schema}
    table_manager_type: hive
    warehouse_directory_path: /user/hive/warehouse
    inject_stats_for_immutable_tables: true
    inject_stats_for_mutable_tables: true
    enforce_non_transactional_tables: false
    metastore:
      host: ${databases.hive.host}
      port: 9083

  presto:
    host: localhost
    port: 8080
    server_address: http://${databases.presto.host}:${databases.presto.port}
    jdbc_driver_class: io.trino.jdbc.TrinoDriver
    jdbc_url: jdbc:trino://${databases.presto.host}:${databases.presto.port}/hive/${databases.hive.schema}
    jdbc_user: hive
    jdbc_password: "***empty***"
    jdbc_pooling: false

  presto_no_default_catalog:
    host: ${databases.presto.host}
    jdbc_driver_class: ${databases.presto.jdbc_driver_class}
    jdbc_url: jdbc:trino://${databases.presto.host}:${databases.presto.port}
    jdbc_user: ${databases.presto.jdbc_user}
    jdbc_password: ${databases.presto.jdbc_password}
    jdbc_pooling: ${databases.presto.${databases.presto.jdbc_user}}

  presto_tpcds:
    host: localhost
    jdbc_driver_class: ${databases.presto.jdbc_driver_class}
    jdbc_url: jdbc:trino://${databases.presto.host}:${databases.presto.port}/hive/tpcds
    jdbc_user: hive
    jdbc_password: "***empty***"
    jdbc_pooling: false

  alice@presto:
    host: ${databases.presto.host}
    port: ${databases.presto.port}
    server_address: ${databases.presto.server_address}
    jdbc_driver_class: ${databases.presto.jdbc_driver_class}
    jdbc_url: ${databases.presto.jdbc_url}
    jdbc_user: alice
    jdbc_password: "***empty***"
    jdbc_pooling: false
    https_keystore_path: ${databases.presto.https_keystore_path}
    https_keystore_password: ${databases.presto.https_keystore_password}

  bob@presto:
    host: ${databases.presto.host}
    port: ${databases.presto.port}
    server_address: ${databases.presto.server_address}
    jdbc_driver_class: ${databases.presto.jdbc_driver_class}
    jdbc_url: ${databases.presto.jdbc_url}
    jdbc_user: bob
    jdbc_password: "***empty***"
    jdbc_pooling: false
    https_keystore_path: ${databases.presto.https_keystore_path}
    https_keystore_password: ${databases.presto.https_keystore_password}

  charlie@presto:
    host: ${databases.presto.host}
    port: ${databases.presto.port}
    server_address: ${databases.presto.server_address}
    jdbc_driver_class: ${databases.presto.jdbc_driver_class}
    jdbc_url: ${databases.presto.jdbc_url}
    jdbc_user: charlie
    jdbc_password: "***empty***"
    jdbc_pooling: false
    https_keystore_path: ${databases.presto.https_keystore_path}
    https_keystore_password: ${databases.presto.https_keystore_password}

  hdfs@presto:
    host: ${databases.presto.host}
    port: ${databases.presto.port}
    server_address: ${databases.presto.server_address}
    jdbc_driver_class: ${databases.presto.jdbc_driver_class}
    jdbc_url: ${databases.presto.jdbc_url}
    jdbc_user: hdfs
    jdbc_password: "***empty***"
    jdbc_pooling: false
    https_keystore_path: ${databases.presto.https_keystore_path}
    https_keystore_password: ${databases.presto.https_keystore_password}

  CaseSensitiveUserName@presto:
    host: ${databases.presto.host}
    port: ${databases.presto.port}
    server_address: ${databases.presto.server_address}
    jdbc_driver_class: ${databases.presto.jdbc_driver_class}
    jdbc_url: ${databases.presto.jdbc_url}
    jdbc_user: CaseSensitiveUserName
    jdbc_password: "***empty***"
    jdbc_pooling: false
    https_keystore_path: ${databases.presto.https_keystore_path}
    https_keystore_password: ${databases.presto.https_keystore_password}

  mysql:
    jdbc_driver_class: com.mysql.jdbc.Driver
    jdbc_url: *****************************
    jdbc_user: root
    jdbc_password: test
    jdbc_pooling: true
    table_manager_type: jdbc

  postgres:
    jdbc_driver_class: org.postgresql.Driver
    jdbc_url: ***************************************
    jdbc_user: test
    jdbc_password: test
    jdbc_pooling: true
    table_manager_type: jdbc

  cassandra:
    host: cassandra
    port: 9042
    default_schema: test
    skip_create_schema: false
    table_manager_type: cassandra

  sqlserver:
    jdbc_driver_class: com.microsoft.sqlserver.jdbc.SQLServerDriver
    jdbc_url: ****************************************
    jdbc_user: sa
    jdbc_password: SQLServerPass1
    jdbc_pooling: true
    table_manager_type: jdbc

  spark:
    host: spark
    jdbc_driver_class: org.apache.hive.jdbc.HiveDriver
    jdbc_url: jdbc:hive2://${databases.spark.host}:10213
    jdbc_user: hive
    jdbc_password: na
    jdbc_pooling: false
    schema: default
    prepare_statement:
      - USE ${databases.spark.schema}
    table_manager_type: jdbc

  kafka:
    broker:
      host: kafka
      port: 9092
    zookeeper:
      host: zookeeper
      port: 2181
    presto_database_name: presto
    presto_kafka_catalog: kafka
    table_manager_type: kafka

tests:
  hdfs:
    path: /tmp/product-test
  assert:
    float_tolerance: 0.000001

ldap:
   admin:
       dn: cn=admin,dc=trino,dc=testldap,dc=com
       password: admin
   url: ldap://ldapserver
