-- delimiter: |; ignoreOrder: true; ignoreExcessRows: true; trimValues:true
 chr | varchar(1) | bigint | scalar | true | Convert Unicode code point to a string |
 concat | varchar | varchar | scalar | true | Concatenates given strings |
 length | bigint | varchar(x) | scalar | true | Count of code points of the given string |
 lower | varchar(x)| varchar(x)| scalar | true | Converts the string to lower case |
 ltrim | varchar(x)| varchar(x)| scalar | true | Removes whitespace from the beginning of a string |
 replace | varchar(x) | varchar(x), varchar(y) | scalar | true | Greedily removes occurrences of a pattern in a string |
 replace | varchar(u) | varchar(x), varchar(y), varchar(z) | scalar | true | Greedily replaces occurrences of a pattern with a string |
 reverse | varchar(x) | varchar(x) | scalar | true | Reverse all code points in a given string |
 rtrim | varchar(x) | varchar(x) | scalar | true | Removes whitespace from the end of a string |
 split | array(varchar(x)) | varchar(x), varchar(y) | scalar | true | |
 split | array(varchar(x)) | varchar(x), varchar(y), bigint | scalar | true | |
 split_part | varchar(x) | varchar(x), varchar(y), bigint | scalar | true | Splits a string by a delimiter and returns the specified field (counting from one) |
 strpos | bigint | varchar(x), varchar(y) | scalar | true | Returns index of first occurrence of a substring (or 0 if not found) |
 strpos | bigint | varchar(x), varchar(y), bigint | scalar | true | Returns index of n-th occurrence of a substring (or 0 if not found) |
 substr | varchar(x) | varchar(x), bigint | scalar | true | Suffix starting at given index |
 substr | varchar(x) | varchar(x), bigint, bigint | scalar | true | Substring of given length starting at an index |
 trim | varchar(x) | varchar(x) | scalar | true | Removes whitespace from the beginning and end of a string |
 upper | varchar(x) | varchar(x) | scalar | true | Converts the string to upper case |
