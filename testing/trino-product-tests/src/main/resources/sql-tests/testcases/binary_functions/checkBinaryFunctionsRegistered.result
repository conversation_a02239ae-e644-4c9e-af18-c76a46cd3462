-- delimiter: |; ignoreOrder: true; ignoreExcessRows: true; trimValues:true
 length | bigint | varbinary | scalar | true | Length of the given binary |
 to_base64 | varchar | varbinary | scalar | true | Encode binary data as base64 |
 to_base64url | varchar | varbinary | scalar | true | Encode binary data as base64 using the URL safe alphabet |
 to_hex | varchar | varbinary | scalar | true | Encode binary data as hex |
 from_base64 | varbinary | varbinary | scalar | true | Decode base64 encoded binary data |
 from_base64 | varbinary | varchar(x) | scalar | true | Decode base64 encoded binary data |
 from_base64url | varbinary | varbinary | scalar | true | Decode URL safe base64 encoded binary data |
 from_base64url | varbinary | varchar(x) | scalar | true | Decode URL safe base64 encoded binary data |
