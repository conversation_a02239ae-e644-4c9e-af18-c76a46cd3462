-- delimiter: |; ignoreOrder: true; ignoreExcessRows: true; trimValues:true
 concat | array(E) | E, array(E) | scalar | true | Concatenates an element to an array |
 concat | array(E) | array(E), E | scalar | true | Concatenates an array to an element |
 concat | array(E) | array(E) | scalar | true | Concatenates given arrays |
 cardinality | bigint | array(e) | scalar | true | Returns the cardinality (length) of the array |
 contains | boolean | array(t), t | scalar | true | Determines whether given value exists in the array |
 array_sort | array(e) | array(e) | scalar | true | Sorts the given array in ascending order according to the natural ordering of its elements. |
 array_sort | array(t) | array(t), function(t,t,integer) | scalar | true | Sorts the given array with a lambda comparator. |
 array_intersect | array(e) | array(e), array(e) | scalar | true | Intersects elements of the two given arrays |
 array_distinct | array(e) | array(e) | scalar | true | Remove duplicate values from the given array |
