-- database: presto; groups: postgresql,profile_specific_tests; tables: postgres.public.workers_psql
--!
select t1.last_name, t2.first_name from postgresql.public.workers_psql t1, postgresql.public.workers_psql t2 where t1.id_department = t2.id_employee
--!
-- delimiter: |; trimValues: true; ignoreOrder: true;
null      | <PERSON>|
<PERSON>    | <PERSON>|
<PERSON>     | <PERSON>|
null      | <PERSON>|
<PERSON><PERSON>     | <PERSON><PERSON>|
<PERSON>     | <PERSON>|
<PERSON>   | <PERSON>|
null      | <PERSON>|
<PERSON>      | <PERSON>|
<PERSON>     | <PERSON>|
<PERSON>     | <PERSON>|
null      | <PERSON>|
