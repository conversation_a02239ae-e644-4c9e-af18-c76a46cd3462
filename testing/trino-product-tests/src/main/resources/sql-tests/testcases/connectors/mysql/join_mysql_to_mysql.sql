-- database: presto; groups: mysql,profile_specific_tests; tables: mysql.test.workers_mysql
--!
select t1.last_name, t2.first_name from mysql.test.workers_mysql t1, mysql.test.workers_mysql t2 where t1.id_department = t2.id_employee
--!
-- delimiter: |; trimValues: true; ignoreOrder: true;
 null      | <PERSON>|
 <PERSON>    | <PERSON>|
 <PERSON>     | <PERSON>|
 null      | <PERSON>|
 <PERSON><PERSON>     | <PERSON><PERSON>|
 <PERSON>     | <PERSON>|
 <PERSON>   | <PERSON>|
 null      | <PERSON>|
 <PERSON>      | <PERSON>|
 <PERSON>     | <PERSON>|
 <PERSON>     | <PERSON>|
 null      | <PERSON>|
