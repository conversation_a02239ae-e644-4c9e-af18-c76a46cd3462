<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>io.trino</groupId>
        <artifactId>trino-root</artifactId>
        <version>372</version>
    </parent>

    <artifactId>trino-docs</artifactId>
    <name>trino-docs</name>
    <packaging>pom</packaging>

    <properties>
        <air.main.basedir>${project.parent.basedir}</air.main.basedir>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.mycila</groupId>
                    <artifactId>license-maven-plugin</artifactId>
                    <configuration>
                        <excludes combine.children="append">
                            <exclude>**/*.conf</exclude>
                            <exclude>**/*.css</exclude>
                            <exclude>**/*.js</exclude>
                        </excludes>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>

        <plugins>
            <plugin>
                <groupId>io.airlift.drift</groupId>
                <artifactId>drift-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>validate</phase>
                        <goals>
                            <goal>generate-thrift-idl</goal>
                        </goals>
                        <configuration>
                            <outputFile>${project.build.directory}/TrinoThriftService.thrift</outputFile>
                            <classes>
                                <class>io.trino.plugin.thrift.api.TrinoThriftService</class>
                            </classes>
                            <recursive>true</recursive>
                        </configuration>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>io.trino</groupId>
                        <artifactId>trino-thrift-api</artifactId>
                        <version>${project.version}</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>exec-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <id>validate-reserved</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>java</goal>
                        </goals>
                        <configuration>
                            <mainClass>io.trino.sql.ReservedIdentifiers</mainClass>
                            <arguments>
                                <argument>validateDocs</argument>
                                <argument>${project.basedir}/src/main/sphinx/language/reserved.rst</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>validate-thrift-idl</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>diff</executable>
                            <arguments>
                                <argument>-b</argument>
                                <argument>-c</argument>
                                <argument>${project.basedir}/src/main/sphinx/include/TrinoThriftService.thrift</argument>
                                <argument>${project.build.directory}/TrinoThriftService.thrift</argument>
                            </arguments>
                        </configuration>
                    </execution>
                    <execution>
                        <id>run-sphinx</id>
                        <phase>package</phase>
                        <goals>
                            <goal>exec</goal>
                        </goals>
                        <configuration>
                            <executable>${project.basedir}/build</executable>
                        </configuration>
                    </execution>
                </executions>
                <configuration>
                    <includeProjectDependencies>false</includeProjectDependencies>
                    <includePluginDependencies>true</includePluginDependencies>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>io.trino</groupId>
                        <artifactId>trino-parser</artifactId>
                        <version>${project.version}</version>
                    </dependency>
                </dependencies>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-assembly-plugin</artifactId>
                <executions>
                    <execution>
                        <id>docs</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <appendAssemblyId>false</appendAssemblyId>
                            <descriptors>
                                <descriptor>src/main/assembly/docs.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>

                    <execution>
                        <id>sources</id>
                        <phase>package</phase>
                        <goals>
                            <goal>single</goal>
                        </goals>
                        <configuration>
                            <descriptors>
                                <descriptor>src/main/assembly/sources.xml</descriptor>
                            </descriptors>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
