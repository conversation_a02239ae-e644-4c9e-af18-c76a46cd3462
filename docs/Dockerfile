# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

FROM sphinxdoc/sphinx:3.3.0

# Required by pillow
RUN apt update -y && apt install -y zlib1g-dev libjpeg-dev build-essential

WORKDIR /docs
ADD requirements.txt /docs
RUN pip3 install -r requirements.txt
