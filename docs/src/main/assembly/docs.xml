<assembly xmlns="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/plugins/maven-assembly-plugin/assembly/1.1.2 http://maven.apache.org/xsd/assembly-1.1.2.xsd">
    <id>docs</id>
    <formats>
        <format>zip</format>
    </formats>
    <includeBaseDirectory>false</includeBaseDirectory>
    <fileSets>
        <fileSet>
            <directory>${project.build.directory}/html</directory>
            <outputDirectory>/html</outputDirectory>
            <excludes>
                <exclude>**/.buildinfo</exclude>
                <exclude>**/.doctrees/**</exclude>
            </excludes>
        </fileSet>
    </fileSets>
</assembly>
