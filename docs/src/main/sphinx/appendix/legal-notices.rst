=============
Legal notices
=============


License
-------

<PERSON><PERSON> is open source software licensed under the
`Apache License 2.0 <https://github.com/trinodb/trino/blob/master/LICENSE>`_.

Code
----

Source code is available at `https://github.com/trinodb
<https://github.com/trinodb>`_.

Governance
----------

The project is run by volunteer contributions and supported by the `Trino
Software Foundation <https://trino.io/foundation.html>`_.

Trademarks
----------

Product names, other names, logos and other material used on this site are
registered trademarks of various entities including, but not limited to, the
following trademark owners and names:

`American National Standards Institute <https://www.ansi.org/>`_

* ANSI, and other names

`Apache Software Foundation <https://apache.org/>`_

* Apache Hadoop, Apache Hive, Apache Iceberg, Apache Kafka, and other names

`Amazon <https://trademarks.amazon.com/>`_

* AWS, S3, <PERSON>lue, E<PERSON>, and other names

`Docker Inc. <https://www.docker.com/>`_

* Docker

`Google <https://www.google.com/permissions/trademark/trademark-list/>`_

* GCP, YouTube and other names

`Linux Mark Institute <http://www.linuxmark.org/>`_

* Linux

`Microsoft <https://www.microsoft.com/en-us/legal/intellectualproperty/Trademarks/EN-US.aspx>`_

* Azure, AKS, and others

`Oracle <https://www.oracle.com/>`_

* Java, JVM, OpenJDK, and other names

`The Linux Foundation <https://www.linuxfoundation.org/trademark-list/>`_

* Kubernetes, Presto, and other names

