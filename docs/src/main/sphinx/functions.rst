***********************
Functions and operators
***********************

This chapter describes the SQL functions and operators supported by Trino. They
allow you to implement complex functionality and behavior of the SQL executed by
Trino operating on the underlying data sources.

If you are looking for a specific function or operator, see the :doc:`full
alphabetical list</functions/list>` or the :doc:`full list by
topic</functions/list-by-topic>`.

Also see the :doc:`SQL data types</language>`
and the :doc:`SQL statement and syntax reference</sql>`.

.. toctree::
    :maxdepth: 1

    Aggregate           <functions/aggregate>
    Array               <functions/array>
    Binary              <functions/binary>
    Bitwise             <functions/bitwise>
    Color               <functions/color>
    Comparison          <functions/comparison>
    Conditional         <functions/conditional>
    Conversion          <functions/conversion>
    Date and time       <functions/datetime>
    Decimal             <functions/decimal>
    Geospatial          <functions/geospatial>
    HyperLogLog         <functions/hyperloglog>
    IP Address          <functions/ipaddress>
    JSON                <functions/json>
    Lambda              <functions/lambda>
    Logical             <functions/logical>
    Machine learning    <functions/ml>
    Map                 <functions/map>
    Math                <functions/math>
    Quantile digest     <functions/qdigest>
    Regular expression  <functions/regexp>
    Session             <functions/session>
    Set Digest          <functions/setdigest>
    String              <functions/string>
    System              <functions/system>
    Teradata            <functions/teradata>
    T-Digest            <functions/tdigest>
    URL                 <functions/url>
    UUID                <functions/uuid>
    Window              <functions/window>
    functions/list
    functions/list-by-topic
