*******
Clients
*******

A client is used to send queries to Trino and receive results, or otherwise
interact with <PERSON><PERSON> and the connected data sources.

Some clients, such as the :doc:`command line interface </installation/cli>`, can
provide a user interface directly. Clients like the :doc:`JDBC driver
</installation/jdbc>`, provide a mechanism for other tools to connect to Trino.

The following clients are available:

.. toctree::
    :maxdepth: 1

    installation/cli
    installation/jdbc

In addition, the community provides `numerous other clients
<https://trino.io/resources.html>`_ for platforms such as Python, and these
can in turn be used to connect applications using these platforms.
