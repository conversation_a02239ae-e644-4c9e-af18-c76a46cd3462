********************
SQL statement syntax
********************

This chapter describes the SQL syntax used in Trino.

A :doc:`reference to the supported SQL data types</language>` is available.

Trino also provides :doc:`numerous SQL functions and operators<functions>`.

.. toctree::
    :maxdepth: 1

    sql/alter-materialized-view
    sql/alter-schema
    sql/alter-table
    sql/alter-view
    sql/analyze
    sql/call
    sql/comment
    sql/commit
    sql/create-materialized-view
    sql/create-role
    sql/create-schema
    sql/create-table
    sql/create-table-as
    sql/create-view
    sql/deallocate-prepare
    sql/delete
    sql/describe
    sql/describe-input
    sql/describe-output
    sql/drop-materialized-view
    sql/drop-role
    sql/drop-schema
    sql/drop-table
    sql/drop-view
    sql/execute
    sql/explain
    sql/explain-analyze
    sql/grant
    sql/grant-roles
    sql/insert
    sql/match-recognize
    sql/pattern-recognition-in-window
    sql/prepare
    sql/refresh-materialized-view
    sql/reset-session
    sql/revoke
    sql/revoke-roles
    sql/rollback
    sql/select
    sql/set-role
    sql/set-session
    sql/set-time-zone
    sql/show-catalogs
    sql/show-columns
    sql/show-create-materialized-view
    sql/show-create-schema
    sql/show-create-table
    sql/show-create-view
    sql/show-functions
    sql/show-grants
    sql/show-role-grants
    sql/show-roles
    sql/show-schemas
    sql/show-session
    sql/show-stats
    sql/show-tables
    sql/start-transaction
    sql/truncate
    sql/update
    sql/use
    sql/values
