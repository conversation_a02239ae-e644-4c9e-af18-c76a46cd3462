**********
Connectors
**********

This chapter describes the connectors available in Trino to access data
from different data sources.

.. toctree::
    :maxdepth: 1

    Accumulo        <connector/accumulo>
    BigQuery        <connector/bigquery>
    Black Hole      <connector/blackhole>
    Cassandra       <connector/cassandra>
    ClickHouse      <connector/clickhouse>
    Druid           <connector/druid>
    Elasticsearch   <connector/elasticsearch>
    Google Sheets   <connector/googlesheets>
    Hive            <connector/hive>
    Iceberg         <connector/iceberg>
    JMX             <connector/jmx>
    Kafka           <connector/kafka>
    Kinesis         <connector/kinesis>
    Kudu            <connector/kudu>
    Local File      <connector/localfile>
    Memory          <connector/memory>
    MongoDB         <connector/mongodb>
    MySQL           <connector/mysql>
    Oracle          <connector/oracle>
    Phoenix         <connector/phoenix>
    Pinot           <connector/pinot>
    PostgreSQL      <connector/postgresql>
    Prometheus      <connector/prometheus>
    Redis           <connector/redis>
    Redshift        <connector/redshift>
    SingleStore (MemSQL)          <connector/memsql>
    SQL Server      <connector/sqlserver>
    System          <connector/system>
    Thrift          <connector/thrift>
    TPCDS           <connector/tpcds>
    TPCH            <connector/tpch>
