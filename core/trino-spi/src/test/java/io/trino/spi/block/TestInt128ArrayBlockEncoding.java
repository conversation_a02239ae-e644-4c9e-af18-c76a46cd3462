/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.trino.spi.block;

import io.trino.spi.type.DecimalType;
import io.trino.spi.type.Int128;
import io.trino.spi.type.Type;

import java.util.Random;

public class TestInt128ArrayBlockEncoding
        extends BaseBlockEncodingTest<Int128>
{
    private static final DecimalType TYPE = DecimalType.createDecimalType(30);

    @Override
    protected Type getType()
    {
        return TYPE;
    }

    @Override
    protected void write(BlockBuilder blockBuilder, Int128 value)
    {
        TYPE.writeObject(blockBuilder, value);
    }

    @Override
    protected Int128 randomValue(Random random)
    {
        return Int128.valueOf(random.nextLong(), random.nextLong());
    }
}
