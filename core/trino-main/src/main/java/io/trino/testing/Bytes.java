/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.trino.testing;

import java.util.Arrays;
import java.util.stream.IntStream;

import static java.lang.String.format;
import static java.util.stream.Collectors.joining;

/**
 * Value-based, immutable {@code byte[]} equivalent.
 */
public final class Bytes
{
    public static Bytes fromBytes(byte... bytes)
    {
        return new Bytes(bytes);
    }

    private final byte[] bytes;

    private Bytes(byte[] bytes)
    {
        this.bytes = bytes.clone();
    }

    public byte[] getBytes()
    {
        return bytes.clone();
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Bytes other = (Bytes) o;
        return Arrays.equals(bytes, other.bytes);
    }

    @Override
    public int hashCode()
    {
        return Arrays.hashCode(bytes);
    }

    @Override
    public String toString()
    {
        return IntStream.range(0, bytes.length)
                .mapToObj(i -> format("%02x", bytes[i] & 0xFF))
                .collect(joining(" ", "X'", "'"));
    }
}
