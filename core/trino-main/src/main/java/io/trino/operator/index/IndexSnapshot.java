/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.trino.operator.index;

import io.trino.operator.join.LookupSource;
import io.trino.spi.Page;
import io.trino.spi.PageBuilder;

import javax.annotation.concurrent.Immutable;

import static java.util.Objects.requireNonNull;

@Immutable
public class IndexSnapshot
        implements IndexedData
{
    private static final Page EMPTY_PAGE = new Page(0);

    private final LookupSource values;
    private final LookupSource missingKeys;

    public IndexSnapshot(LookupSource values, LookupSource missingKeys)
    {
        this.values = requireNonNull(values, "values is null");
        this.missingKeys = requireNonNull(missingKeys, "missingKeys is null");
    }

    @Override
    public long getJoinPosition(int position, Page page)
    {
        long joinPosition = values.getJoinPosition(position, page, page);
        if (joinPosition < 0) {
            if (missingKeys.getJoinPosition(position, page, page) < 0) {
                return UNLOADED_INDEX_KEY;
            }
            return NO_MORE_POSITIONS;
        }
        return joinPosition;
    }

    @Override
    public long getNextJoinPosition(long currentPosition)
    {
        return values.getNextJoinPosition(currentPosition, -1, EMPTY_PAGE);
    }

    @Override
    public void appendTo(long position, PageBuilder pageBuilder, int outputChannelOffset)
    {
        values.appendTo(position, pageBuilder, outputChannelOffset);
    }

    @Override
    public void close()
    {
    }
}
