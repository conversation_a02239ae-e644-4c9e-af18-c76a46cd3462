/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.trino.operator.join;

import io.trino.operator.OperatorFactory;
import io.trino.operator.PipelineExecutionStrategy;

import java.util.Optional;

import static java.util.Objects.requireNonNull;

public interface JoinOperatorFactory
        extends OperatorFactory
{
    Optional<OuterOperatorFactoryResult> createOuterOperatorFactory();

    class OuterOperatorFactoryResult
    {
        private final OperatorFactory outerOperatorFactory;
        private final PipelineExecutionStrategy buildExecutionStrategy;

        public OuterOperatorFactoryResult(OperatorFactory outerOperatorFactory, PipelineExecutionStrategy buildExecutionStrategy)
        {
            this.outerOperatorFactory = requireNonNull(outerOperatorFactory, "outerOperatorFactory is null");
            this.buildExecutionStrategy = requireNonNull(buildExecutionStrategy, "buildExecutionStrategy is null");
        }

        public OperatorFactory getOuterOperatorFactory()
        {
            return outerOperatorFactory;
        }

        public PipelineExecutionStrategy getBuildExecutionStrategy()
        {
            return buildExecutionStrategy;
        }
    }
}
