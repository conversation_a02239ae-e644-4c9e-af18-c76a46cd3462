/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.trino.metadata;

import io.trino.spi.exchange.ExchangeManagerHandleResolver;
import io.trino.spi.exchange.ExchangeSinkInstanceHandle;
import io.trino.spi.exchange.ExchangeSourceHandle;

import java.util.concurrent.atomic.AtomicReference;

import static com.google.common.base.Preconditions.checkState;

public class ExchangeHandleResolver
{
    private final AtomicReference<ExchangeManagerHandleResolver> exchangeManagerHandleResolver = new AtomicReference<>();

    public void setExchangeManagerHandleResolver(ExchangeManagerHandleResolver resolver)
    {
        checkState(exchangeManagerHandleResolver.compareAndSet(null, resolver), "ExchangeManagerHandleResolver is already set");
    }

    public Class<? extends ExchangeSinkInstanceHandle> getExchangeSinkInstanceHandleClass()
    {
        ExchangeManagerHandleResolver resolver = exchangeManagerHandleResolver.get();
        checkState(resolver != null, "ExchangeManagerHandleResolver is not set");
        return resolver.getExchangeSinkInstanceHandleClass();
    }

    public Class<? extends ExchangeSourceHandle> getExchangeSourceHandleClass()
    {
        ExchangeManagerHandleResolver resolver = exchangeManagerHandleResolver.get();
        checkState(resolver != null, "ExchangeManagerHandleResolver is not set");
        return resolver.getExchangeSourceHandleHandleClass();
    }
}
