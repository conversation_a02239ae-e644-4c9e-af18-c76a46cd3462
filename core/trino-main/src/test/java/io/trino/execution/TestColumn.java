/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.trino.execution;

import io.airlift.json.JsonCodec;
import org.testng.annotations.Test;

import static org.testng.Assert.assertEquals;

public class TestColumn
{
    private static final JsonCodec<Column> codec = JsonCodec.jsonCodec(Column.class);

    @Test
    public void testRoundTrip()
    {
        Column expected = new Column("name", "type");
        String json = codec.toJson(expected);
        Column actual = codec.fromJson(json);

        assertEquals(actual, expected);
    }
}
