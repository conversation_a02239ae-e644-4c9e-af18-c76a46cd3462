/*
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package io.trino.connector;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.trino.spi.connector.ConnectorOutputTableHandle;
import io.trino.spi.connector.SchemaTableName;

import java.util.Objects;

import static java.util.Objects.requireNonNull;

public class MockConnectorOutputTableHandle
        implements ConnectorOutputTableHandle
{
    private final SchemaTableName tableName;

    @JsonCreator
    public MockConnectorOutputTableHandle(@JsonProperty("tableHandle") SchemaTableName tableName)
    {
        this.tableName = requireNonNull(tableName, "tableName is null");
    }

    @JsonProperty
    public SchemaTableName getTableName()
    {
        return tableName;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        MockConnectorOutputTableHandle other = (MockConnectorOutputTableHandle) o;
        return Objects.equals(tableName, other.tableName);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(tableName);
    }
}
